export class Grid {
    constructor(width, height) {
        this.width = width;
        this.height = height;
        this.cells = [];
        this.init();
    }
    
    init() {
        // Initialize 2D array
        for (let x = 0; x < this.width; x++) {
            this.cells[x] = [];
            for (let y = 0; y < this.height; y++) {
                this.cells[x][y] = null;
            }
        }
    }
    
    clear() {
        this.init();
    }
    
    isValidPosition(x, y) {
        return x >= 0 && x < this.width && y >= 0 && y < this.height;
    }
    
    getCell(x, y) {
        if (!this.isValidPosition(x, y)) {
            return null;
        }
        return this.cells[x][y];
    }
    
    setCell(x, y, entity) {
        if (this.isValidPosition(x, y)) {
            this.cells[x][y] = entity;
        }
    }
    
    removeCell(x, y) {
        if (this.isValidPosition(x, y)) {
            this.cells[x][y] = null;
        }
    }
    
    isEmpty(x, y) {
        return this.getCell(x, y) === null;
    }
    
    // Get all entities in a specific area
    getEntitiesInArea(x, y, width, height) {
        const entities = [];
        
        for (let i = x; i < x + width && i < this.width; i++) {
            for (let j = y; j < y + height && j < this.height; j++) {
                const entity = this.getCell(i, j);
                if (entity) {
                    entities.push(entity);
                }
            }
        }
        
        return entities;
    }
    
    // Get all entities of a specific type
    getEntitiesByType(type) {
        const entities = [];
        
        for (let x = 0; x < this.width; x++) {
            for (let y = 0; y < this.height; y++) {
                const entity = this.getCell(x, y);
                if (entity && entity.type === type) {
                    entities.push(entity);
                }
            }
        }
        
        return entities;
    }
    
    // Find the nearest entity of a specific type
    findNearestEntity(fromX, fromY, type) {
        let nearest = null;
        let minDistance = Infinity;
        
        for (let x = 0; x < this.width; x++) {
            for (let y = 0; y < this.height; y++) {
                const entity = this.getCell(x, y);
                if (entity && entity.type === type) {
                    const distance = Math.abs(x - fromX) + Math.abs(y - fromY);
                    if (distance < minDistance) {
                        minDistance = distance;
                        nearest = entity;
                    }
                }
            }
        }
        
        return nearest;
    }
    
    // Get adjacent cells (up, down, left, right)
    getAdjacentCells(x, y) {
        const adjacent = [];
        const directions = [
            {x: 0, y: -1}, // up
            {x: 0, y: 1},  // down
            {x: -1, y: 0}, // left
            {x: 1, y: 0}   // right
        ];
        
        directions.forEach(dir => {
            const newX = x + dir.x;
            const newY = y + dir.y;
            if (this.isValidPosition(newX, newY)) {
                adjacent.push({
                    x: newX,
                    y: newY,
                    entity: this.getCell(newX, newY)
                });
            }
        });
        
        return adjacent;
    }
    
    // Get all cells in a cross pattern (for explosions)
    getCrossPattern(centerX, centerY, range) {
        const cells = [];
        const directions = [
            {x: 0, y: -1}, // up
            {x: 0, y: 1},  // down
            {x: -1, y: 0}, // left
            {x: 1, y: 0}   // right
        ];
        
        // Add center cell
        if (this.isValidPosition(centerX, centerY)) {
            cells.push({x: centerX, y: centerY, entity: this.getCell(centerX, centerY)});
        }
        
        // Add cells in each direction
        directions.forEach(dir => {
            for (let i = 1; i <= range; i++) {
                const x = centerX + (dir.x * i);
                const y = centerY + (dir.y * i);
                
                if (!this.isValidPosition(x, y)) break;
                
                const entity = this.getCell(x, y);
                cells.push({x, y, entity});
                
                // Stop if we hit a solid block
                if (entity && entity.type === 'solid') break;
            }
        });
        
        return cells;
    }
    
    // Pathfinding helper - check if path is clear
    isPathClear(fromX, fromY, toX, toY) {
        // Simple line-of-sight check
        const dx = Math.abs(toX - fromX);
        const dy = Math.abs(toY - fromY);
        const stepX = fromX < toX ? 1 : -1;
        const stepY = fromY < toY ? 1 : -1;
        
        let x = fromX;
        let y = fromY;
        
        while (x !== toX || y !== toY) {
            if (dx > dy) {
                x += stepX;
                if (!this.isEmpty(x, y)) return false;
            } else {
                y += stepY;
                if (!this.isEmpty(x, y)) return false;
            }
        }
        
        return true;
    }
    
    // Debug method to visualize grid
    debugPrint() {
        console.log('Grid state:');
        for (let y = 0; y < this.height; y++) {
            let row = '';
            for (let x = 0; x < this.width; x++) {
                const entity = this.getCell(x, y);
                if (entity) {
                    row += entity.type.charAt(0).toUpperCase() + ' ';
                } else {
                    row += '. ';
                }
            }
            console.log(row);
        }
    }
}
