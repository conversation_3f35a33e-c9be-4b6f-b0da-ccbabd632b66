export class InputManager {
    constructor() {
        this.keys = new Map(); // Current key states
        this.keyBindings = new Map(); // Player key bindings
        this.players = new Map(); // Player references
        
        // Input state tracking
        this.pressedKeys = new Set();
        this.releasedKeys = new Set();
        this.heldKeys = new Set();
        
        // Repeat handling
        this.repeatDelay = 150; // ms before repeat starts
        this.repeatRate = 100; // ms between repeats
        this.keyRepeatTimers = new Map();
        
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        // Keyboard events
        document.addEventListener('keydown', (e) => this.handleKeyDown(e));
        document.addEventListener('keyup', (e) => this.handleKeyUp(e));
        
        // Prevent default behavior for game keys
        document.addEventListener('keydown', (e) => {
            if (this.isGameKey(e.code)) {
                e.preventDefault();
            }
        });
        
        // Handle window focus/blur to reset key states
        window.addEventListener('blur', () => this.resetAllKeys());
        window.addEventListener('focus', () => this.resetAllKeys());
    }
    
    handleKeyDown(event) {
        const keyCode = event.code;
        
        // Ignore if key is already pressed (prevents key repeat from OS)
        if (this.heldKeys.has(keyCode)) return;
        
        // Update key states
        this.pressedKeys.add(keyCode);
        this.heldKeys.add(keyCode);
        this.keys.set(keyCode, {
            pressed: true,
            held: true,
            timestamp: performance.now()
        });
        
        // Handle immediate action
        this.processKeyAction(keyCode, 'press');
        
        // Setup repeat timer
        this.setupKeyRepeat(keyCode);
    }
    
    handleKeyUp(event) {
        const keyCode = event.code;
        
        // Update key states
        this.releasedKeys.add(keyCode);
        this.heldKeys.delete(keyCode);
        this.keys.set(keyCode, {
            pressed: false,
            held: false,
            timestamp: performance.now()
        });
        
        // Clear repeat timer
        this.clearKeyRepeat(keyCode);
        
        // Handle release action
        this.processKeyAction(keyCode, 'release');
    }
    
    setupKeyRepeat(keyCode) {
        // Clear existing timer
        this.clearKeyRepeat(keyCode);
        
        // Set up new repeat timer
        const timer = setTimeout(() => {
            if (this.heldKeys.has(keyCode)) {
                this.processKeyAction(keyCode, 'repeat');
                
                // Set up continuous repeat
                const repeatTimer = setInterval(() => {
                    if (this.heldKeys.has(keyCode)) {
                        this.processKeyAction(keyCode, 'repeat');
                    } else {
                        clearInterval(repeatTimer);
                    }
                }, this.repeatRate);
                
                this.keyRepeatTimers.set(keyCode, repeatTimer);
            }
        }, this.repeatDelay);
        
        this.keyRepeatTimers.set(keyCode, timer);
    }
    
    clearKeyRepeat(keyCode) {
        const timer = this.keyRepeatTimers.get(keyCode);
        if (timer) {
            clearTimeout(timer);
            clearInterval(timer);
            this.keyRepeatTimers.delete(keyCode);
        }
    }
    
    processKeyAction(keyCode, actionType) {
        // Find which player this key belongs to
        for (const [playerId, bindings] of this.keyBindings) {
            const player = this.players.get(playerId);
            if (!player) continue;
            
            // Check each binding for this player
            for (const [action, boundKey] of Object.entries(bindings)) {
                if (boundKey === keyCode) {
                    this.executePlayerAction(player, action, actionType);
                    break;
                }
            }
        }
    }
    
    executePlayerAction(player, action, actionType) {
        // Only process press and repeat actions for movement and bomb
        if (actionType === 'release') return;
        
        switch (action) {
            case 'up':
                player.move('up');
                break;
            case 'down':
                player.move('down');
                break;
            case 'left':
                player.move('left');
                break;
            case 'right':
                player.move('right');
                break;
            case 'bomb':
                if (actionType === 'press') { // Only on initial press, not repeat
                    player.placeBomb();
                }
                break;
        }
    }
    
    bindPlayer(player, keyBindings) {
        const playerId = player.playerId;
        this.players.set(playerId, player);
        this.keyBindings.set(playerId, keyBindings);
    }
    
    unbindPlayer(playerId) {
        this.players.delete(playerId);
        this.keyBindings.delete(playerId);
    }
    
    // Check if a key is currently pressed
    isKeyPressed(keyCode) {
        return this.pressedKeys.has(keyCode);
    }
    
    // Check if a key is currently held
    isKeyHeld(keyCode) {
        return this.heldKeys.has(keyCode);
    }
    
    // Check if a key was just released
    isKeyReleased(keyCode) {
        return this.releasedKeys.has(keyCode);
    }
    
    // Get key state
    getKeyState(keyCode) {
        return this.keys.get(keyCode) || { pressed: false, held: false, timestamp: 0 };
    }
    
    // Check if key is a game key (to prevent default browser behavior)
    isGameKey(keyCode) {
        const gameKeys = new Set();
        
        // Add all bound keys
        for (const bindings of this.keyBindings.values()) {
            Object.values(bindings).forEach(key => gameKeys.add(key));
        }
        
        // Add common game keys
        gameKeys.add('ArrowUp');
        gameKeys.add('ArrowDown');
        gameKeys.add('ArrowLeft');
        gameKeys.add('ArrowRight');
        gameKeys.add('Space');
        gameKeys.add('Enter');
        gameKeys.add('Escape');
        
        return gameKeys.has(keyCode);
    }
    
    // Reset all key states (useful for focus/blur events)
    resetAllKeys() {
        this.pressedKeys.clear();
        this.releasedKeys.clear();
        this.heldKeys.clear();
        this.keys.clear();
        
        // Clear all repeat timers
        for (const timer of this.keyRepeatTimers.values()) {
            clearTimeout(timer);
            clearInterval(timer);
        }
        this.keyRepeatTimers.clear();
    }
    
    // Clear frame-specific key states (call at end of each frame)
    clearFrameStates() {
        this.pressedKeys.clear();
        this.releasedKeys.clear();
    }
    
    // Get all currently held keys
    getHeldKeys() {
        return Array.from(this.heldKeys);
    }
    
    // Get player bindings
    getPlayerBindings(playerId) {
        return this.keyBindings.get(playerId);
    }
    
    // Update key bindings for a player
    updatePlayerBindings(playerId, newBindings) {
        if (this.players.has(playerId)) {
            this.keyBindings.set(playerId, newBindings);
        }
    }
    
    // Check for key conflicts between players
    checkKeyConflicts() {
        const usedKeys = new Map(); // key -> [playerIds]
        const conflicts = [];
        
        for (const [playerId, bindings] of this.keyBindings) {
            for (const [action, key] of Object.entries(bindings)) {
                if (!usedKeys.has(key)) {
                    usedKeys.set(key, []);
                }
                usedKeys.get(key).push({ playerId, action });
            }
        }
        
        for (const [key, users] of usedKeys) {
            if (users.length > 1) {
                conflicts.push({ key, users });
            }
        }
        
        return conflicts;
    }
    
    // Debug method to show current input state
    debugState() {
        return {
            pressedKeys: Array.from(this.pressedKeys),
            heldKeys: Array.from(this.heldKeys),
            releasedKeys: Array.from(this.releasedKeys),
            playerCount: this.players.size,
            keyBindings: Object.fromEntries(this.keyBindings),
            conflicts: this.checkKeyConflicts()
        };
    }
}
