import { Entity } from './Entity.js';

export class Block extends Entity {
    constructor(x, y, blockType = 'solid') {
        super(x, y, blockType);
        this.blockType = blockType; // 'solid' or 'destructible'
        this.health = blockType === 'destructible' ? 1 : Infinity;
        this.maxHealth = this.health;
        
        // Visual state
        this.isDamaged = false;
        this.isDestroyed = false;
        
        // Animation for destruction
        this.destructionAnimation = {
            isPlaying: false,
            duration: 300, // ms
            elapsed: 0,
            frames: ['crack1', 'crack2', 'crack3', 'destroyed']
        };
        
        this.setupBlockElement();
    }
    
    setupBlockElement() {
        this.element.classList.add(`block-${this.blockType}`);
        
        if (this.blockType === 'solid') {
            this.element.innerHTML = `
                <div class="block-face">
                    <div class="block-texture"></div>
                    <div class="block-highlight"></div>
                </div>
            `;
        } else {
            this.element.innerHTML = `
                <div class="block-face destructible">
                    <div class="block-texture"></div>
                    <div class="block-cracks"></div>
                    <div class="block-highlight"></div>
                </div>
            `;
        }
        
        this.element.style.zIndex = '1';
    }
    
    update(deltaTime) {
        super.update(deltaTime);
        
        if (this.destructionAnimation.isPlaying) {
            this.updateDestructionAnimation(deltaTime);
        }
    }
    
    updateDestructionAnimation(deltaTime) {
        this.destructionAnimation.elapsed += deltaTime;
        const progress = this.destructionAnimation.elapsed / this.destructionAnimation.duration;
        
        if (progress >= 1) {
            this.destructionAnimation.isPlaying = false;
            this.completeDestruction();
        } else {
            // Update animation frame
            const frameIndex = Math.floor(progress * this.destructionAnimation.frames.length);
            const frame = this.destructionAnimation.frames[Math.min(frameIndex, this.destructionAnimation.frames.length - 1)];
            this.element.classList.add(frame);
        }
    }
    
    takeDamage(amount = 1) {
        if (this.blockType === 'solid' || this.isDestroyed) {
            return false; // Solid blocks can't be damaged
        }
        
        this.health -= amount;
        this.isDamaged = true;
        
        if (this.health <= 0) {
            this.startDestruction();
            return true; // Block was destroyed
        }
        
        // Update visual damage state
        this.updateDamageVisual();
        return false; // Block was damaged but not destroyed
    }
    
    updateDamageVisual() {
        if (this.blockType === 'destructible' && this.isDamaged) {
            const damagePercent = 1 - (this.health / this.maxHealth);
            this.element.classList.add('damaged');
            
            // Add crack visual based on damage
            const cracksElement = this.element.querySelector('.block-cracks');
            if (cracksElement) {
                cracksElement.style.opacity = damagePercent;
            }
        }
    }
    
    startDestruction() {
        if (this.isDestroyed || this.destructionAnimation.isPlaying) return;
        
        this.destructionAnimation.isPlaying = true;
        this.destructionAnimation.elapsed = 0;
        this.element.classList.add('destroying');
    }
    
    completeDestruction() {
        this.isDestroyed = true;
        this.isActive = false;
        this.element.classList.add('destroyed');
        
        // Fade out effect
        this.element.style.transition = 'opacity 0.2s ease-out';
        this.element.style.opacity = '0';
        
        setTimeout(() => {
            this.destroy();
        }, 200);
    }
    
    render() {
        super.render();
        
        if (!this.isVisible || this.isDestroyed) return;
        
        // Update block appearance based on state
        let className = `entity ${this.type} block-${this.blockType}`;
        
        if (this.isDamaged) {
            className += ' damaged';
        }
        
        if (this.destructionAnimation.isPlaying) {
            className += ' destroying';
            
            // Add current animation frame
            const progress = this.destructionAnimation.elapsed / this.destructionAnimation.duration;
            const frameIndex = Math.floor(progress * this.destructionAnimation.frames.length);
            const frame = this.destructionAnimation.frames[Math.min(frameIndex, this.destructionAnimation.frames.length - 1)];
            className += ` ${frame}`;
        }
        
        this.element.className = className;
    }
    
    // Check if block can be destroyed
    isDestructible() {
        return this.blockType === 'destructible';
    }
    
    // Check if block is solid (blocks movement)
    isSolid() {
        return !this.isDestroyed;
    }
    
    // Check if block blocks explosions
    blocksExplosion() {
        return this.blockType === 'solid' || !this.isDestroyed;
    }
    
    // Get block info
    getInfo() {
        return {
            position: {x: this.x, y: this.y},
            type: this.blockType,
            health: this.health,
            maxHealth: this.maxHealth,
            isDamaged: this.isDamaged,
            isDestroyed: this.isDestroyed,
            isDestructible: this.isDestructible(),
            isSolid: this.isSolid()
        };
    }
    
    // Static factory methods for easier creation
    static createSolid(x, y) {
        return new Block(x, y, 'solid');
    }
    
    static createDestructible(x, y) {
        return new Block(x, y, 'destructible');
    }
}

// Export both the main class and convenience classes
export class SolidBlock extends Block {
    constructor(x, y) {
        super(x, y, 'solid');
    }
}

export class DestructibleBlock extends Block {
    constructor(x, y) {
        super(x, y, 'destructible');
    }
}
