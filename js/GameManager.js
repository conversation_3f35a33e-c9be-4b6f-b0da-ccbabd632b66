import { Grid } from './utils/Grid.js';
import { InputManager } from './utils/InputManager.js';
import { Player } from './entities/Player.js';
import { Block } from './entities/Block.js';

export class GameManager {
    constructor() {
        this.gameContainer = document.getElementById('game-grid');
        this.grid = new Grid(13, 11); // Standard Bomberman grid
        this.inputManager = new InputManager();
        
        // Game state
        this.isRunning = false;
        this.isPaused = false;
        this.lastTime = 0;
        this.targetFPS = 60;
        this.frameInterval = 1000 / this.targetFPS;
        
        // Entity collections
        this.players = [];
        this.bombs = [];
        this.blocks = [];
        this.explosions = [];
        this.powerups = [];
        
        // Game stats
        this.score = 0;
        this.level = 1;
        this.lives = 3;
        
        this.init();
    }
    
    init() {
        this.setupGameGrid();
        this.createLevel();
        this.setupPlayers();
        this.bindEvents();
    }
    
    setupGameGrid() {
        this.gameContainer.style.display = 'grid';
        this.gameContainer.style.gridTemplateColumns = `repeat(${this.grid.width}, 1fr)`;
        this.gameContainer.style.gridTemplateRows = `repeat(${this.grid.height}, 1fr)`;
        this.gameContainer.style.gap = '0';
        this.gameContainer.style.width = '650px';
        this.gameContainer.style.height = '550px';
        this.gameContainer.style.position = 'relative';
    }
    
    createLevel() {
        // Create border blocks (solid)
        for (let x = 0; x < this.grid.width; x++) {
            for (let y = 0; y < this.grid.height; y++) {
                if (x === 0 || x === this.grid.width - 1 || y === 0 || y === this.grid.height - 1) {
                    this.addBlock(x, y, 'solid');
                } else if (x % 2 === 0 && y % 2 === 0) {
                    // Add solid blocks in even positions (classic Bomberman pattern)
                    this.addBlock(x, y, 'solid');
                } else if (Math.random() < 0.6 && !this.isPlayerStartPosition(x, y)) {
                    // Add destructible blocks randomly, avoiding player start positions
                    this.addBlock(x, y, 'destructible');
                }
            }
        }
    }
    
    isPlayerStartPosition(x, y) {
        // Player 1 starts at (1,1), Player 2 at (11,9), etc.
        const startPositions = [
            {x: 1, y: 1},
            {x: this.grid.width - 2, y: this.grid.height - 2},
            {x: 1, y: this.grid.height - 2},
            {x: this.grid.width - 2, y: 1}
        ];
        
        return startPositions.some(pos => 
            Math.abs(pos.x - x) <= 1 && Math.abs(pos.y - y) <= 1
        );
    }
    
    setupPlayers() {
        // Create player 1
        const player1 = new Player(1, 1, 1, this);
        this.players.push(player1);
        this.addEntity(player1);
        
        // Setup input bindings for player 1
        this.inputManager.bindPlayer(player1, {
            up: 'ArrowUp',
            down: 'ArrowDown',
            left: 'ArrowLeft',
            right: 'ArrowRight',
            bomb: ' ' // Spacebar
        });
    }
    
    addBlock(x, y, type) {
        const block = new Block(x, y, type);
        this.blocks.push(block);
        this.grid.setCell(x, y, block);
        this.addEntity(block);
    }
    
    addEntity(entity) {
        this.gameContainer.appendChild(entity.element);
        entity.render();
    }
    
    removeEntity(entity) {
        if (entity.element && entity.element.parentNode) {
            entity.element.parentNode.removeChild(entity.element);
        }
    }
    
    bindEvents() {
        // Pause/Resume
        document.addEventListener('keydown', (e) => {
            if (e.code === 'Escape') {
                this.togglePause();
            }
        });
        
        // Window focus/blur
        window.addEventListener('blur', () => this.pause());
        window.addEventListener('focus', () => this.resume());
    }
    
    start() {
        this.isRunning = true;
        this.isPaused = false;
        this.lastTime = performance.now();
        this.gameLoop();
    }
    
    pause() {
        this.isPaused = true;
    }
    
    resume() {
        if (this.isRunning) {
            this.isPaused = false;
            this.lastTime = performance.now();
        }
    }
    
    togglePause() {
        if (this.isPaused) {
            this.resume();
        } else {
            this.pause();
        }
    }
    
    stop() {
        this.isRunning = false;
        this.isPaused = false;
    }
    
    gameLoop(currentTime = performance.now()) {
        if (!this.isRunning) return;
        
        const deltaTime = currentTime - this.lastTime;
        
        if (deltaTime >= this.frameInterval && !this.isPaused) {
            this.update(deltaTime);
            this.render();
            this.lastTime = currentTime;
        }
        
        requestAnimationFrame((time) => this.gameLoop(time));
    }
    
    update(deltaTime) {
        // Update all entities
        this.players.forEach(player => player.update(deltaTime));
        this.bombs.forEach(bomb => bomb.update(deltaTime));
        this.explosions.forEach(explosion => explosion.update(deltaTime));
        
        // Clean up expired entities
        this.cleanupExpiredEntities();
        
        // Check game conditions
        this.checkGameConditions();
    }
    
    render() {
        // Entities handle their own rendering
        this.players.forEach(player => player.render());
        this.bombs.forEach(bomb => bomb.render());
        this.blocks.forEach(block => block.render());
        this.explosions.forEach(explosion => explosion.render());
        
        // Update UI
        this.updateUI();
    }
    
    cleanupExpiredEntities() {
        // Remove expired explosions
        this.explosions = this.explosions.filter(explosion => {
            if (explosion.isExpired()) {
                this.removeEntity(explosion);
                return false;
            }
            return true;
        });
        
        // Remove exploded bombs
        this.bombs = this.bombs.filter(bomb => {
            if (bomb.hasExploded()) {
                this.removeEntity(bomb);
                return false;
            }
            return true;
        });
    }
    
    checkGameConditions() {
        // Check if all players are dead
        const alivePlayers = this.players.filter(player => player.isAlive());
        if (alivePlayers.length === 0) {
            this.gameOver();
        }
        
        // Check if level is complete (all destructible blocks destroyed)
        const destructibleBlocks = this.blocks.filter(block => block.type === 'destructible');
        if (destructibleBlocks.length === 0) {
            this.levelComplete();
        }
    }
    
    updateUI() {
        const scoreElement = document.getElementById('score');
        const livesElement = document.getElementById('lives');
        const levelElement = document.getElementById('level');
        
        if (scoreElement) scoreElement.textContent = `Score: ${this.score}`;
        if (livesElement) livesElement.textContent = `Lives: ${this.lives}`;
        if (levelElement) levelElement.textContent = `Level: ${this.level}`;
    }
    
    gameOver() {
        this.stop();
        alert('Game Over!');
    }
    
    levelComplete() {
        this.level++;
        this.score += 1000;
        // Reset level
        this.blocks = [];
        this.bombs = [];
        this.explosions = [];
        this.gameContainer.innerHTML = '';
        this.grid.clear();
        this.createLevel();
        this.setupPlayers();
    }
    
    // Utility methods for entities
    canMoveTo(x, y) {
        return this.grid.isValidPosition(x, y) && !this.grid.getCell(x, y);
    }
    
    getEntitiesAt(x, y) {
        const entities = [];
        
        // Check for blocks
        const block = this.grid.getCell(x, y);
        if (block) entities.push(block);
        
        // Check for bombs
        this.bombs.forEach(bomb => {
            if (bomb.x === x && bomb.y === y) entities.push(bomb);
        });
        
        // Check for players
        this.players.forEach(player => {
            if (player.x === x && player.y === y) entities.push(player);
        });
        
        return entities;
    }
}
