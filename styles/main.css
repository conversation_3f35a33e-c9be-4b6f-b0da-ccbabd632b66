/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    color: white;
}

/* Game container */
#game-container {
    background: rgba(0, 0, 0, 0.8);
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

#game-grid {
    width: 650px;
    height: 550px;
    background: #2c3e50;
    border: 3px solid #34495e;
    border-radius: 5px;
    position: relative;
    overflow: hidden;
}

/* Game UI */
#game-ui {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
    padding: 10px;
    background: rgba(52, 73, 94, 0.8);
    border-radius: 5px;
    font-weight: bold;
    font-size: 18px;
}

/* Base entity styles */
.entity {
    position: absolute;
    width: 50px;
    height: 50px;
    transition: all 0.1s ease-out;
}

/* Block styles */
.block-solid {
    background: linear-gradient(135deg, #7f8c8d 0%, #95a5a6 50%, #7f8c8d 100%);
    border: 2px solid #34495e;
    border-radius: 3px;
}

.block-solid .block-face {
    width: 100%;
    height: 100%;
    position: relative;
    background: linear-gradient(45deg, #bdc3c7 25%, transparent 25%),
                linear-gradient(-45deg, #bdc3c7 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, #bdc3c7 75%),
                linear-gradient(-45deg, transparent 75%, #bdc3c7 75%);
    background-size: 8px 8px;
    background-position: 0 0, 0 4px, 4px -4px, -4px 0px;
}

.block-solid .block-highlight {
    position: absolute;
    top: 2px;
    left: 2px;
    right: 2px;
    height: 8px;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.3), transparent);
    border-radius: 2px;
}

.block-destructible {
    background: linear-gradient(135deg, #8b4513 0%, #a0522d 50%, #8b4513 100%);
    border: 2px solid #654321;
    border-radius: 3px;
}

.block-destructible .block-face {
    width: 100%;
    height: 100%;
    position: relative;
    background: repeating-linear-gradient(
        45deg,
        #cd853f,
        #cd853f 3px,
        #daa520 3px,
        #daa520 6px
    );
}

.block-destructible .block-cracks {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 50 50"><path d="M10,5 L15,15 L25,10 L35,20 L40,15 M5,25 L20,30 L30,25 L45,35" stroke="black" stroke-width="1" fill="none" opacity="0.6"/></svg>');
    opacity: 0;
    transition: opacity 0.3s ease;
}

.block-destructible.damaged .block-cracks {
    opacity: 0.8;
}

/* Player styles */
.player {
    z-index: 10;
}

.player-sprite {
    width: 100%;
    height: 100%;
    position: relative;
}

.player-body {
    width: 40px;
    height: 40px;
    margin: 5px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    border: 3px solid #1abc9c;
    position: relative;
}

.player-1 .player-body {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    border-color: #f39c12;
}

.player-2 .player-body {
    background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
    border-color: #e67e22;
}

.player-face {
    position: absolute;
    top: 8px;
    left: 8px;
    width: 20px;
    height: 20px;
    background: #f1c40f;
    border-radius: 50%;
    border: 2px solid #f39c12;
}

.player-face::before {
    content: '';
    position: absolute;
    top: 4px;
    left: 4px;
    width: 3px;
    height: 3px;
    background: #2c3e50;
    border-radius: 50%;
    box-shadow: 6px 0 0 #2c3e50, 3px 6px 0 #e74c3c;
}

/* Player animations */
.player.walking .player-body {
    animation: playerWalk 0.3s ease-in-out infinite alternate;
}

@keyframes playerWalk {
    0% { transform: translateY(0px) scale(1, 1); }
    100% { transform: translateY(-2px) scale(1.05, 0.95); }
}

.player.dead {
    opacity: 0.5;
    transform: scale(0.8);
    filter: grayscale(100%);
}

/* Bomb styles */
.bomb {
    z-index: 5;
}

.bomb-body {
    width: 40px;
    height: 40px;
    margin: 5px;
    background: radial-gradient(circle at 30% 30%, #2c3e50, #1a252f);
    border-radius: 50%;
    border: 3px solid #34495e;
    position: relative;
    overflow: hidden;
}

.bomb-fuse {
    position: absolute;
    top: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 15px;
    background: linear-gradient(to bottom, #f39c12, #e67e22);
    border-radius: 2px;
    transition: height 0.1s ease;
}

.bomb-highlight {
    position: absolute;
    top: 8px;
    left: 8px;
    width: 12px;
    height: 12px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3), transparent);
    border-radius: 50%;
}

/* Bomb animations */
.bomb.pulse-1 {
    animation: bombPulse1 0.5s ease-in-out;
}

.bomb.pulse-2 {
    animation: bombPulse2 0.5s ease-in-out;
}

@keyframes bombPulse1 {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes bombPulse2 {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(0.95); }
}

.bomb.warning {
    animation: bombWarning 0.2s ease-in-out infinite alternate;
}

@keyframes bombWarning {
    0% { 
        filter: brightness(1);
        box-shadow: 0 0 0 rgba(231, 76, 60, 0.7);
    }
    100% { 
        filter: brightness(1.3);
        box-shadow: 0 0 20px rgba(231, 76, 60, 0.7);
    }
}

/* Explosion styles */
.explosion {
    z-index: 8;
    pointer-events: none;
}

.explosion-core {
    width: 100%;
    height: 100%;
    position: relative;
    border-radius: 50%;
}

.explosion-inner {
    position: absolute;
    top: 10%;
    left: 10%;
    width: 80%;
    height: 80%;
    background: radial-gradient(circle, #fff 0%, #ffeb3b 30%, #ff9800 60%, #f44336 100%);
    border-radius: 50%;
    animation: explosionInner 0.8s ease-out;
}

.explosion-outer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, transparent 40%, #ff5722 50%, #ff9800 70%, transparent 100%);
    border-radius: 50%;
    animation: explosionOuter 0.8s ease-out;
}

.explosion-sparks {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, transparent 60%, #ffeb3b 65%, transparent 70%);
    border-radius: 50%;
    animation: explosionSparks 0.8s ease-out;
}

@keyframes explosionInner {
    0% { transform: scale(0.1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.9; }
    100% { transform: scale(0.8); opacity: 0; }
}

@keyframes explosionOuter {
    0% { transform: scale(0.5); opacity: 0.8; }
    50% { transform: scale(1.5); opacity: 0.6; }
    100% { transform: scale(2); opacity: 0; }
}

@keyframes explosionSparks {
    0% { transform: scale(0.8) rotate(0deg); opacity: 1; }
    50% { transform: scale(1.3) rotate(180deg); opacity: 0.7; }
    100% { transform: scale(1.8) rotate(360deg); opacity: 0; }
}

/* Explosion intensity classes */
.explosion.intensity-high .explosion-inner {
    background: radial-gradient(circle, #fff 0%, #ffeb3b 20%, #ff5722 50%, #d32f2f 100%);
}

.explosion.intensity-medium .explosion-inner {
    background: radial-gradient(circle, #ffeb3b 0%, #ff9800 40%, #f44336 80%, #c62828 100%);
}

.explosion.intensity-low .explosion-inner {
    background: radial-gradient(circle, #ff9800 0%, #f44336 50%, #c62828 100%);
}

.explosion.intensity-minimal .explosion-inner {
    background: radial-gradient(circle, #f44336 0%, #c62828 70%, #b71c1c 100%);
}

/* Explosion direction variants */
.explosion-horizontal .explosion-core {
    border-radius: 20px;
    transform: scaleX(2) scaleY(0.7);
}

.explosion-vertical .explosion-core {
    border-radius: 20px;
    transform: scaleX(0.7) scaleY(2);
}

.explosion-end .explosion-core {
    transform: scale(0.8);
}

/* Responsive design */
@media (max-width: 768px) {
    #game-container {
        padding: 10px;
        margin: 10px;
    }
    
    #game-grid {
        width: 520px;
        height: 440px;
    }
    
    .entity {
        width: 40px;
        height: 40px;
    }
    
    #game-ui {
        font-size: 16px;
    }
}

/* Utility classes */
.hidden {
    display: none !important;
}

.fade-in {
    animation: fadeIn 0.3s ease-in;
}

.fade-out {
    animation: fadeOut 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}
